#!/usr/bin/env python3
"""
Create a GIF from the generated frames using PIL
This doesn't require ffmpeg and creates a simple animated GIF
"""

import os
import glob
from PIL import Image

def create_gif_from_frames():
    """Create an animated GIF from the generated frames"""
    print("🎬 Creating animated GIF from frames...")
    
    # Find all frame files
    frame_pattern = "output/quick_test_frames_*.png"
    frame_files = sorted(glob.glob(frame_pattern))
    
    if not frame_files:
        print("❌ No frame files found!")
        return False
    
    print(f"✅ Found {len(frame_files)} frames")
    
    # Load all frames
    frames = []
    for frame_file in frame_files:
        try:
            img = Image.open(frame_file)
            frames.append(img)
            print(f"📸 Loaded: {os.path.basename(frame_file)}")
        except Exception as e:
            print(f"❌ Error loading {frame_file}: {e}")
            return False
    
    if not frames:
        print("❌ No frames loaded!")
        return False
    
    # Get dimensions from first frame
    width, height = frames[0].size
    print(f"📐 Frame size: {width}x{height}")
    
    # Create animated GIF
    output_file = "quick_test_vertical_video.gif"
    try:
        frames[0].save(
            output_file,
            save_all=True,
            append_images=frames[1:],
            duration=250,  # 250ms per frame = 4 fps
            loop=0  # Loop forever
        )
        
        print(f"✅ Animated GIF created: {output_file}")
        
        # Get file size
        file_size = os.path.getsize(output_file) / 1024  # KB
        print(f"📁 File size: {file_size:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating GIF: {e}")
        return False

def main():
    """Main function"""
    print("🎥 GIF Creator for Vertical Video Test")
    print("=" * 50)
    
    success = create_gif_from_frames()
    
    if success:
        print("\n🎉 SUCCESS! Vertical video test completed!")
        print("\n📋 What was created:")
        print("• Animated GIF: quick_test_vertical_video.gif")
        print("• Format: 256x456 (vertical)")
        print("• Duration: ~2 seconds")
        print("• Frame rate: 4 fps")
        
        print("\n✅ This proves your setup works perfectly!")
        print("\n🔄 For production videos, you can:")
        print("1. Use higher resolution (576x1024 or 1080x1920)")
        print("2. Generate more frames (25-60)")
        print("3. Use better quality settings")
        print("4. Export as MP4 with ffmpeg")
        
        print("\n📱 The GIF is perfect for:")
        print("• Quick previews")
        print("• Social media posts")
        print("• Testing animations")
        
    else:
        print("\n❌ Failed to create GIF")
        print("Make sure the frame generation completed successfully")
    
    return success

if __name__ == "__main__":
    main()
