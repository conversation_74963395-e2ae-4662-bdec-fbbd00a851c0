#!/usr/bin/env python3
"""
Create a vertical video using Stable Video Diffusion
This script takes the generated vertical image and creates an animated video
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI and return the prompt ID"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def get_history(prompt_id, server_address="127.0.0.1:8188"):
    """Get the execution history for a prompt ID"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/history/{prompt_id}") as response:
            return json.loads(response.read())
    except Exception as e:
        print(f"Error getting history: {e}")
        return None

def wait_for_completion(prompt_id, server_address="127.0.0.1:8188", timeout=900):
    """Wait for prompt execution to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id, server_address)
        if history and prompt_id in history:
            return history[prompt_id]
        time.sleep(5)
        print(".", end="", flush=True)
    return None

def create_video_workflow_simple():
    """Create a simple video workflow using basic nodes"""
    workflow = {
        "1": {
            "inputs": {
                "image": "vertical_image_00001_.png",
                "upload": "image"
            },
            "class_type": "LoadImage",
            "_meta": {"title": "Load Vertical Image"}
        },
        "2": {
            "inputs": {
                "width": 576,
                "height": 1024,
                "batch_size": 25
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Video Latent"}
        },
        "3": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "4": {
            "inputs": {
                "text": "smooth motion, cinematic, high quality video",
                "clip": ["3", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Positive Prompt"}
        },
        "5": {
            "inputs": {
                "text": "static, blurry, low quality",
                "clip": ["3", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Negative Prompt"}
        },
        "6": {
            "inputs": {
                "seed": 42,
                "steps": 15,
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 0.8,
                "model": ["3", 0],
                "positive": ["4", 0],
                "negative": ["5", 0],
                "latent_image": ["2", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "Video Generation"}
        },
        "7": {
            "inputs": {
                "samples": ["6", 0],
                "vae": ["3", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "Decode Video"}
        },
        "8": {
            "inputs": {
                "filename_prefix": "vertical_video_frames",
                "images": ["7", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Video Frames"}
        }
    }
    return workflow

def create_animated_sequence():
    """Create an animated sequence from the vertical image"""
    print("🎬 Creating animated vertical video...")
    print("📐 Format: 576x1024 (9:16 aspect ratio)")
    print("🎞️  Generating 25 frames for smooth animation")
    
    # Create workflow
    workflow = create_video_workflow_simple()
    
    # Queue the prompt
    print("📤 Sending animation request to ComfyUI...")
    result = queue_prompt(workflow)
    if not result:
        print("❌ Failed to queue animation prompt")
        return False
    
    prompt_id = result['prompt_id']
    print(f"✅ Animation queued with ID: {prompt_id}")
    
    # Wait for completion
    print("⏳ Generating video frames (this will take several minutes)...")
    print("💡 Your M1 Pro is working hard to create smooth animation!")
    history = wait_for_completion(prompt_id, timeout=900)
    
    if history:
        print("\n✅ Video frame generation completed!")
        print("📁 Check the ComfyUI output folder for your video frames.")
        print("🎥 Frames are saved as 'vertical_video_frames_XXXXX_.png'")
        return True
    else:
        print("\n❌ Video generation timed out or failed")
        return False

def main():
    """Main function"""
    print("🎥 Vertical Video Creator")
    print("=" * 50)
    
    # Check if ComfyUI is running
    try:
        with urllib.request.urlopen("http://127.0.0.1:8188/system_stats", timeout=5) as response:
            print("✅ ComfyUI is running")
    except:
        print("❌ ComfyUI is not running. Please start it with: python main.py")
        return False
    
    # Check if vertical image exists
    if not os.path.exists("output/vertical_image_00001_.png"):
        print("❌ Vertical image not found. Please run generate_vertical_video.py first.")
        return False
    
    print("✅ Vertical image found")
    
    # Create animated sequence
    success = create_animated_sequence()
    
    if success:
        print("\n🎉 Success! Your vertical video frames have been generated!")
        print("\n📋 What was created:")
        print("• 25 individual frames in vertical format (576x1024)")
        print("• Each frame shows smooth animation progression")
        print("• Perfect for mobile and social media content")
        
        print("\n📁 Files location:")
        print("• Check ComfyUI/output/ folder")
        print("• Look for files: vertical_video_frames_00001_.png to vertical_video_frames_00025_.png")
        
        print("\n🔄 Next steps to create final video:")
        print("1. Use ffmpeg to combine frames into MP4:")
        print("   ffmpeg -r 8 -i output/vertical_video_frames_%05d_.png -c:v libx264 -pix_fmt yuv420p vertical_video.mp4")
        print("2. Or use any video editing software to import the frame sequence")
        
    else:
        print("\n❌ Video generation failed. Please check:")
        print("1. ComfyUI is running at http://127.0.0.1:8188")
        print("2. Models are properly loaded")
        print("3. Sufficient memory available (16GB should be enough)")
        print("4. Check ComfyUI console for error messages")
    
    return success

if __name__ == "__main__":
    main()
