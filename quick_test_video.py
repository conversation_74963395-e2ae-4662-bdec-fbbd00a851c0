#!/usr/bin/env python3
"""
Quick test video generator - Super fast, low quality for testing only
Creates a 2-second vertical video at very low resolution for quick verification
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI and return the prompt ID"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def get_history(prompt_id, server_address="127.0.0.1:8188"):
    """Get the execution history for a prompt ID"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/history/{prompt_id}") as response:
            return json.loads(response.read())
    except Exception as e:
        print(f"Error getting history: {e}")
        return None

def wait_for_completion(prompt_id, server_address="127.0.0.1:8188", timeout=120):
    """Wait for prompt execution to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id, server_address)
        if history and prompt_id in history:
            return history[prompt_id]
        time.sleep(2)
        print(".", end="", flush=True)
    return None

def create_quick_test_workflow():
    """Create a super fast test workflow - minimal quality for speed"""
    workflow = {
        "1": {
            "inputs": {
                "width": 256,  # Very low resolution for speed
                "height": 456,  # Vertical but small
                "batch_size": 8  # Only 8 frames = ~1 second at 8fps
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Quick Test Latent"}
        },
        "2": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "3": {
            "inputs": {
                "text": "simple landscape",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Simple Prompt"}
        },
        "4": {
            "inputs": {
                "text": "blurry",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Negative"}
        },
        "5": {
            "inputs": {
                "seed": 42,
                "steps": 8,  # Very few steps for speed
                "cfg": 6.0,  # Lower CFG for speed
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 0.6,  # Lower denoise for speed
                "model": ["2", 0],
                "positive": ["3", 0],
                "negative": ["4", 0],
                "latent_image": ["1", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "Fast Sampler"}
        },
        "6": {
            "inputs": {
                "samples": ["5", 0],
                "vae": ["2", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "Decode"}
        },
        "7": {
            "inputs": {
                "filename_prefix": "quick_test_frames",
                "images": ["6", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Frames"}
        }
    }
    return workflow

def create_quick_test():
    """Create a quick test video"""
    print("⚡ Quick Test Video Generator")
    print("📐 Format: 256x456 (vertical, very low res)")
    print("🎞️  Generating 8 frames (~1 second)")
    print("⚡ Super fast settings for testing only!")
    
    # Create workflow
    workflow = create_quick_test_workflow()
    
    # Queue the prompt
    print("📤 Sending quick test to ComfyUI...")
    result = queue_prompt(workflow)
    if not result:
        print("❌ Failed to queue test")
        return False
    
    prompt_id = result['prompt_id']
    print(f"✅ Test queued with ID: {prompt_id}")
    
    # Wait for completion
    print("⏳ Generating (should be very fast)...")
    history = wait_for_completion(prompt_id, timeout=120)
    
    if history:
        print("\n✅ Quick test completed!")
        return True
    else:
        print("\n❌ Test timed out")
        return False

def combine_frames_to_video():
    """Combine the generated frames into a video using ffmpeg"""
    print("\n🎬 Creating video from frames...")
    
    # Check if frames exist
    frame_files = [f for f in os.listdir("output") if f.startswith("quick_test_frames_")]
    if not frame_files:
        print("❌ No frames found")
        return False
    
    print(f"✅ Found {len(frame_files)} frames")
    
    # Create video with ffmpeg
    cmd = [
        "ffmpeg", "-y",  # Overwrite output
        "-r", "4",  # 4 fps for very short video
        "-i", "output/quick_test_frames_%05d_.png",
        "-c:v", "libx264",
        "-pix_fmt", "yuv420p",
        "-t", "2",  # Limit to 2 seconds
        "quick_test_vertical_video.mp4"
    ]
    
    try:
        import subprocess
        result = subprocess.run(cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Video created: quick_test_vertical_video.mp4")
            return True
        else:
            print(f"❌ FFmpeg error: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found. Install with: brew install ffmpeg")
        print("📁 Frames are saved in output/ folder - you can manually combine them")
        return False

def main():
    """Main function"""
    print("⚡ QUICK TEST - Vertical Video Generator")
    print("=" * 50)
    print("🚀 This creates a very low quality, short video for testing")
    
    # Check if ComfyUI is running
    try:
        with urllib.request.urlopen("http://127.0.0.1:8188/system_stats", timeout=5) as response:
            print("✅ ComfyUI is running")
    except:
        print("❌ ComfyUI is not running. Please start it with: python main.py")
        return False
    
    # Create quick test
    success = create_quick_test()
    
    if success:
        print("\n🎉 Frame generation successful!")
        
        # Try to create video
        video_success = combine_frames_to_video()
        
        if video_success:
            print("\n🎥 SUCCESS! Quick test video created!")
            print("📁 File: quick_test_vertical_video.mp4")
            print("📐 Resolution: 256x456 (vertical)")
            print("⏱️  Duration: ~2 seconds")
            print("🎯 This proves the video generation pipeline works!")
        else:
            print("\n✅ Frames generated successfully!")
            print("📁 Check output/ folder for quick_test_frames_*.png")
            print("💡 You can manually combine frames or install ffmpeg")
        
        print("\n🔄 For high quality videos, use the full scripts with:")
        print("• Higher resolution (576x1024 or 1080x1920)")
        print("• More frames (25-60)")
        print("• More steps (20-50)")
        print("• Higher CFG (7-12)")
        
    else:
        print("\n❌ Quick test failed. Check ComfyUI console for errors.")
    
    return success

if __name__ == "__main__":
    main()
