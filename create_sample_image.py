#!/usr/bin/env python3
"""
Create a sample image for testing image-to-video functionality
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_image(filename="sample_image.jpg", width=1024, height=576, vertical=False):
    """Create a simple sample image"""
    # Create a new image with a gradient background
    img = Image.new('RGB', (width, height), color='skyblue')
    draw = ImageDraw.Draw(img)
    
    # Create a simple landscape scene
    # Sky gradient
    for y in range(height // 2):
        color_intensity = int(255 - (y * 100 / (height // 2)))
        color = (135, 206, max(235 - y // 2, 180))  # Sky blue gradient
        draw.line([(0, y), (width, y)], fill=color)
    
    # Ground
    for y in range(height // 2, height):
        color = (34, 139, 34)  # Forest green
        draw.line([(0, y), (width, y)], fill=color)
    
    # Mountains
    mountain_points = [
        (0, height // 2),
        (width // 4, height // 4),
        (width // 2, height // 3),
        (3 * width // 4, height // 5),
        (width, height // 2)
    ]
    draw.polygon(mountain_points, fill=(105, 105, 105))  # Gray mountains
    
    # Sun
    sun_x, sun_y = width - 150, 100
    draw.ellipse([sun_x - 40, sun_y - 40, sun_x + 40, sun_y + 40], fill='yellow')
    
    # Add some text
    try:
        # Try to use a default font
        font = ImageFont.load_default()
        draw.text((50, height - 100), "Sample Image for Video Generation", fill='white', font=font)
    except:
        # If font loading fails, just draw without font
        draw.text((50, height - 100), "Sample Image for Video Generation", fill='white')
    
    # Save the image
    img.save(filename, 'JPEG', quality=95)
    print(f"✅ Sample image created: {filename}")
    print(f"   Size: {width}x{height}")
    return filename

if __name__ == "__main__":
    # Create horizontal sample
    create_sample_image()

    # Create vertical sample
    create_sample_image("sample_image_vertical.jpg", width=576, height=1024, vertical=True)
