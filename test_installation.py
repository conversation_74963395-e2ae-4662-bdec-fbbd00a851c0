#!/usr/bin/env python3
"""
Test script to verify ComfyUI installation and basic functionality
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os

def test_comfyui_connection(server_address="127.0.0.1:8188"):
    """Test if ComfyUI is running and accessible"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/system_stats", timeout=5) as response:
            data = json.loads(response.read())
            print("✅ ComfyUI is running and accessible")
            print(f"   System info: {data}")
            return True
    except Exception as e:
        print(f"❌ Cannot connect to ComfyUI: {e}")
        return False

def test_models():
    """Check if required models are available"""
    models_dir = "models/checkpoints"
    required_models = [
        "v1-5-pruned-emaonly.safetensors",
        "svd_xt.safetensors"
    ]
    
    print("\n📁 Checking models:")
    all_models_present = True
    
    for model in required_models:
        model_path = os.path.join(models_dir, model)
        if os.path.exists(model_path):
            size_mb = os.path.getsize(model_path) / (1024 * 1024)
            print(f"   ✅ {model} ({size_mb:.1f} MB)")
        else:
            print(f"   ❌ {model} - Not found")
            all_models_present = False
    
    return all_models_present

def create_simple_test_workflow():
    """Create a simple workflow to test basic functionality"""
    workflow = {
        "1": {
            "inputs": {
                "text": "a beautiful landscape",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Prompt)"}
        },
        "2": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "3": {
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Empty Latent Image"}
        },
        "4": {
            "inputs": {
                "seed": 42,
                "steps": 10,  # Reduced steps for quick test
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["2", 0],
                "positive": ["1", 0],
                "negative": ["5", 0],
                "latent_image": ["3", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "5": {
            "inputs": {
                "text": "",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Negative)"}
        },
        "6": {
            "inputs": {
                "samples": ["4", 0],
                "vae": ["2", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "VAE Decode"}
        },
        "7": {
            "inputs": {
                "filename_prefix": "test_output",
                "images": ["6", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Image"}
        }
    }
    return workflow

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def test_generation():
    """Test basic image generation"""
    print("\n🎨 Testing basic image generation...")
    
    workflow = create_simple_test_workflow()
    result = queue_prompt(workflow)
    
    if result:
        print(f"   ✅ Test prompt queued successfully (ID: {result.get('prompt_id', 'unknown')})")
        print("   Check ComfyUI interface or output folder for the generated test image")
        return True
    else:
        print("   ❌ Failed to queue test prompt")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing ComfyUI Installation")
    print("=" * 40)
    
    # Test 1: Connection
    if not test_comfyui_connection():
        print("\n❌ ComfyUI is not running. Please start it with: python main.py")
        return False
    
    # Test 2: Models
    models_ok = test_models()
    if not models_ok:
        print("\n⚠️  Some models are missing. Download may still be in progress.")
    
    # Test 3: Basic generation (only if models are available)
    if models_ok:
        generation_ok = test_generation()
    else:
        print("\n⏭️  Skipping generation test (models not ready)")
        generation_ok = False
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 Test Summary:")
    print(f"   Connection: {'✅' if True else '❌'}")
    print(f"   Models: {'✅' if models_ok else '⚠️'}")
    print(f"   Generation: {'✅' if generation_ok else '⏭️'}")
    
    if models_ok and generation_ok:
        print("\n🎉 All tests passed! Your ComfyUI installation is ready.")
        print("\nYou can now use:")
        print("   • python simple_t2v.py 'your prompt' - for text-to-video")
        print("   • python simple_i2v.py image.jpg - for image-to-video")
        print("   • python simple_v2v.py video.mp4 'style' - for video-to-video")
    else:
        print("\n⚠️  Installation incomplete. Please wait for model downloads to finish.")
    
    return models_ok and generation_ok

if __name__ == "__main__":
    main()
