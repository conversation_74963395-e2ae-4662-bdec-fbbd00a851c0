#!/usr/bin/env python3
"""
Show help and examples for the video generator
"""

def show_help():
    print("""
🎬 VIDEO GENERATOR - Complete Control Panel
============================================

🚀 QUICK START EXAMPLES:

# Fast test (30 seconds)
python video_generator.py --prompt "sunset" --format vertical --resolution low --duration 1 --fps 4 --create-gif

# Social media ready
python video_generator.py --prompt "coffee shop aesthetic" --format vertical --resolution medium --duration 3 --fps 8 --create-gif

# High quality
python video_generator.py --prompt "ocean waves" --format horizontal --resolution high --duration 5 --fps 12 --steps 30 --create-gif

📋 ALL ARGUMENTS:

REQUIRED:
  --prompt "text description"     What you want to generate

FORMAT & SIZE:
  --format vertical|horizontal|square|custom    Video orientation
  --resolution low|medium|high|custom           Quality level
  --custom-width 800 --custom-height 600       Custom dimensions

TIMING:
  --duration 2.0                  Video length in seconds
  --fps 8                         Frames per second

QUALITY:
  --steps 20                      Sampling steps (8=fast, 50=best)
  --cfg 7.0                       Prompt strength (3=creative, 15=strict)
  --denoise 0.8                   Animation amount (0.1=static, 1.0=fluid)

ADVANCED:
  --negative-prompt "blurry"      What to avoid
  --seed 42                       Random seed (same=reproducible)
  --sampler euler                 Sampling method
  --model v1-5-pruned-emaonly.safetensors    AI model
  --output-prefix my_video        Output filename
  --create-gif                    Make animated GIF
  --timeout 1800                  Max wait time (seconds)

📐 RESOLUTION GUIDE:

Vertical (Mobile/TikTok):
  low: 256x456, medium: 576x1024, high: 1080x1920

Horizontal (YouTube/Web):
  low: 456x256, medium: 1024x576, high: 1920x1080

Square (Instagram):
  low: 256x256, medium: 512x512, high: 1024x1024

⚡ SPEED vs QUALITY:

FAST (30 sec):     --resolution low --duration 1 --fps 4 --steps 8
BALANCED (3 min):  --resolution medium --duration 2 --fps 8 --steps 20
QUALITY (15 min):  --resolution high --duration 3 --fps 12 --steps 30
PREMIUM (45 min):  --resolution high --duration 5 --fps 24 --steps 50

🎯 COMMON USE CASES:

TikTok/Instagram Stories:
python video_generator.py --prompt "aesthetic cafe" --format vertical --resolution medium --duration 3 --fps 10 --create-gif

YouTube Shorts:
python video_generator.py --prompt "cooking tutorial" --format vertical --resolution high --duration 5 --fps 15 --create-gif

Instagram Posts:
python video_generator.py --prompt "product showcase" --format square --resolution medium --duration 2 --fps 8 --create-gif

Web Content:
python video_generator.py --prompt "landscape timelapse" --format horizontal --resolution high --duration 4 --fps 12 --create-gif

💡 PRO TIPS:

1. Always test with --resolution low first
2. Use motion words: "flowing", "moving", "dynamic"
3. Add style: "cinematic", "aesthetic", "professional"
4. Specify lighting: "golden hour", "soft lighting", "dramatic"
5. For smooth animation, use higher --denoise (0.8-1.0)

🔧 TROUBLESHOOTING:

Too slow? → Use --resolution low, reduce --steps and --duration
Poor quality? → Increase --steps, use --resolution high
No animation? → Increase --denoise, add motion words to prompt
Out of memory? → Use --resolution low or medium

📁 OUTPUT FILES:

Frames: ComfyUI/output/[prefix]_00001_.png, _00002_.png, etc.
GIF: ComfyUI/[prefix]_video.gif (if --create-gif used)

🎬 READY TO CREATE? Try this:
python video_generator.py --prompt "your amazing idea" --format vertical --resolution medium --duration 2 --fps 8 --create-gif
""")

if __name__ == "__main__":
    show_help()
