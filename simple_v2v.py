#!/usr/bin/env python3
"""
Simple Video-to-Video (V2V) Script using ComfyUI
This script takes an input video and transforms it using AI models.
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI and return the prompt ID"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def get_history(prompt_id, server_address="127.0.0.1:8188"):
    """Get the execution history for a prompt ID"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/history/{prompt_id}") as response:
            return json.loads(response.read())
    except Exception as e:
        print(f"Error getting history: {e}")
        return None

def wait_for_completion(prompt_id, server_address="127.0.0.1:8188", timeout=900):
    """Wait for prompt execution to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id, server_address)
        if history and prompt_id in history:
            return history[prompt_id]
        time.sleep(5)
    return None

def upload_video(video_path, server_address="127.0.0.1:8188"):
    """Upload a video to ComfyUI"""
    try:
        # Note: This is a simplified version. ComfyUI's actual upload API might differ
        print(f"Video loaded: {video_path}")
        return os.path.basename(video_path)
    except Exception as e:
        print(f"Error uploading video: {e}")
        return None

def create_v2v_workflow(video_filename, prompt_text="", strength=0.7, output_filename="output_video.mp4"):
    """Create a video-to-video workflow"""
    workflow = {
        "1": {
            "inputs": {
                "video": video_filename,
                "force_rate": 0,
                "force_size": "Disabled",
                "custom_width": 512,
                "custom_height": 512,
                "frame_load_cap": 0,
                "skip_first_frames": 0,
                "select_every_nth": 1
            },
            "class_type": "VHS_LoadVideo",
            "_meta": {"title": "Load Video"}
        },
        "2": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "3": {
            "inputs": {
                "text": prompt_text,
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Prompt)"}
        },
        "4": {
            "inputs": {
                "text": "",
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Negative)"}
        },
        "5": {
            "inputs": {
                "pixels": ["1", 0],
                "vae": ["2", 2]
            },
            "class_type": "VAEEncode",
            "_meta": {"title": "VAE Encode"}
        },
        "6": {
            "inputs": {
                "seed": 42,
                "steps": 20,
                "cfg": 7.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": strength,
                "model": ["2", 0],
                "positive": ["3", 0],
                "negative": ["4", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "7": {
            "inputs": {
                "samples": ["6", 0],
                "vae": ["2", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "VAE Decode"}
        },
        "8": {
            "inputs": {
                "filename_prefix": "v2v_output",
                "fps": 8,
                "lossless": False,
                "quality": 85,
                "method": "h264-mp4",
                "images": ["7", 0]
            },
            "class_type": "VHS_VideoCombine",
            "_meta": {"title": "Video Combine"}
        }
    }
    return workflow

def video_to_video(video_path, prompt_text="", strength=0.7, output_filename="output_video.mp4"):
    """Transform a video using AI"""
    print(f"Transforming video: {video_path}")
    print(f"Using prompt: '{prompt_text}'")
    print(f"Strength: {strength}")
    
    # Check if video exists
    if not os.path.exists(video_path):
        print(f"Error: Video file '{video_path}' not found")
        return False
    
    # Upload video (simplified - in practice you'd use ComfyUI's upload API)
    video_filename = upload_video(video_path)
    if not video_filename:
        print("Failed to upload video")
        return False
    
    # Create workflow
    workflow = create_v2v_workflow(video_filename, prompt_text, strength, output_filename)
    
    # Queue the prompt
    result = queue_prompt(workflow)
    if not result:
        print("Failed to queue prompt")
        return False
    
    prompt_id = result['prompt_id']
    print(f"Prompt queued with ID: {prompt_id}")
    
    # Wait for completion
    print("Waiting for video transformation to complete...")
    print("This may take several minutes depending on video length and your hardware...")
    history = wait_for_completion(prompt_id, timeout=900)
    
    if history:
        print("✅ Video transformation completed!")
        print("Check the ComfyUI output folder for your transformed video.")
        return True
    else:
        print("❌ Generation timed out or failed")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python simple_v2v.py path/to/video.mp4 [prompt] [strength]")
        print("Example: python simple_v2v.py input_video.mp4 'cartoon style' 0.7")
        sys.exit(1)
    
    video_path = sys.argv[1]
    prompt_text = sys.argv[2] if len(sys.argv) > 2 else ""
    strength = float(sys.argv[3]) if len(sys.argv) > 3 else 0.7
    
    success = video_to_video(video_path, prompt_text, strength)
    
    if success:
        print("\n🎉 Success! Check the ComfyUI output folder for your transformed video.")
        print("Note: Make sure you have the VHS extension installed for video processing.")
    else:
        print("\n❌ Failed to transform video. Make sure:")
        print("1. ComfyUI is running")
        print("2. Video file exists and is accessible")
        print("3. VHS extension is installed for video processing")
