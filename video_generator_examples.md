# Video Generator - Complete Usage Guide

## 🚀 Quick Start

```bash
# Basic vertical video (recommended for testing)
python video_generator.py --prompt "beautiful sunset over mountains"

# Quick test (fast generation)
python video_generator.py --prompt "ocean waves" --format vertical --resolution low --duration 1 --fps 4 --create-gif
```

## 📋 All Available Arguments

### **Required Arguments**
- `--prompt` - Text description for video generation

### **Format & Resolution**
- `--format` - Video orientation
  - `vertical` (9:16) - Perfect for mobile, TikTok, Instagram Stories
  - `horizontal` (16:9) - Standard widescreen, YouTube
  - `square` (1:1) - Instagram posts, profile videos
  - `custom` - Use custom dimensions

- `--resolution` - Quality level
  - `low` - Fast generation, smaller files
  - `medium` - Balanced quality/speed (recommended)
  - `high` - Best quality, slower generation
  - `custom` - Use custom width/height

- `--custom-width` / `--custom-height` - Custom dimensions (pixels)

### **Timing Controls**
- `--duration` - Video length in seconds (default: 2.0)
- `--fps` - Frames per second (default: 8)

### **Quality Settings**
- `--steps` - Sampling steps (8-50, default: 20)
  - Lower = faster, higher = better quality
- `--cfg` - CFG scale (1-20, default: 7.0)
  - Higher = more prompt adherence
- `--denoise` - Denoise strength (0.1-1.0, default: 0.8)
  - Lower = more variation between frames

### **Advanced Options**
- `--negative-prompt` - What to avoid in generation
- `--seed` - Random seed for reproducible results
- `--sampler` - Sampling method (euler, dpm, etc.)
- `--scheduler` - Noise scheduler
- `--model` - AI model to use
- `--output-prefix` - Output filename prefix
- `--create-gif` - Create animated GIF
- `--timeout` - Max generation time (seconds)

## 🎯 Resolution Guide

### Vertical (9:16)
- **Low**: 256x456 (testing)
- **Medium**: 576x1024 (social media)
- **High**: 1080x1920 (professional)

### Horizontal (16:9)
- **Low**: 456x256 (testing)
- **Medium**: 1024x576 (web)
- **High**: 1920x1080 (HD video)

### Square (1:1)
- **Low**: 256x256 (testing)
- **Medium**: 512x512 (social posts)
- **High**: 1024x1024 (high quality)

## ⚡ Performance Guide

### Fast Generation (Testing)
```bash
python video_generator.py \
  --prompt "your prompt" \
  --resolution low \
  --duration 1 \
  --fps 4 \
  --steps 8 \
  --cfg 6 \
  --create-gif
```

### Balanced Quality
```bash
python video_generator.py \
  --prompt "your prompt" \
  --format vertical \
  --resolution medium \
  --duration 3 \
  --fps 8 \
  --steps 20 \
  --cfg 7 \
  --create-gif
```

### High Quality
```bash
python video_generator.py \
  --prompt "your prompt" \
  --format vertical \
  --resolution high \
  --duration 5 \
  --fps 12 \
  --steps 30 \
  --cfg 10 \
  --create-gif
```

### Professional Quality
```bash
python video_generator.py \
  --prompt "cinematic landscape, golden hour" \
  --format horizontal \
  --resolution high \
  --duration 4 \
  --fps 24 \
  --steps 50 \
  --cfg 12 \
  --negative-prompt "blurry, low quality, distorted, artifacts" \
  --create-gif
```

## 🎨 Example Commands

### Social Media Content
```bash
# TikTok/Instagram Stories
python video_generator.py \
  --prompt "trendy cafe interior, aesthetic lighting" \
  --format vertical \
  --resolution medium \
  --duration 3 \
  --fps 10 \
  --create-gif

# YouTube Shorts
python video_generator.py \
  --prompt "cooking process, food preparation" \
  --format vertical \
  --resolution high \
  --duration 5 \
  --fps 15 \
  --create-gif

# Instagram Post
python video_generator.py \
  --prompt "product showcase, minimalist background" \
  --format square \
  --resolution medium \
  --duration 2 \
  --fps 8 \
  --create-gif
```

### Creative Content
```bash
# Abstract Art
python video_generator.py \
  --prompt "flowing abstract colors, fluid motion" \
  --format square \
  --resolution high \
  --duration 4 \
  --fps 12 \
  --steps 40 \
  --create-gif

# Nature Scene
python video_generator.py \
  --prompt "peaceful forest, morning mist, cinematic" \
  --format horizontal \
  --resolution high \
  --duration 6 \
  --fps 24 \
  --steps 35 \
  --cfg 9 \
  --create-gif

# Urban Scene
python video_generator.py \
  --prompt "cyberpunk city, neon lights, rain" \
  --format vertical \
  --resolution high \
  --duration 4 \
  --fps 15 \
  --steps 30 \
  --negative-prompt "blurry, low quality, static" \
  --create-gif
```

### Custom Resolutions
```bash
# Custom aspect ratio
python video_generator.py \
  --prompt "panoramic landscape" \
  --format custom \
  --custom-width 1200 \
  --custom-height 400 \
  --duration 3 \
  --fps 8 \
  --create-gif

# Ultra-wide
python video_generator.py \
  --prompt "epic mountain range" \
  --format custom \
  --custom-width 2560 \
  --custom-height 1080 \
  --duration 4 \
  --fps 10 \
  --create-gif
```

## ⏱️ Generation Time Estimates (M1 Pro)

| Resolution | Duration | FPS | Steps | Est. Time |
|------------|----------|-----|-------|-----------|
| Low        | 1s       | 4   | 8     | 30 sec    |
| Medium     | 2s       | 8   | 20    | 3 min     |
| Medium     | 3s       | 8   | 20    | 5 min     |
| High       | 3s       | 12  | 30    | 15 min    |
| High       | 5s       | 24  | 50    | 45 min    |

## 🎛️ Parameter Effects

### Steps (Quality vs Speed)
- **8-12**: Very fast, basic quality
- **15-25**: Good balance (recommended)
- **30-40**: High quality, slower
- **50+**: Maximum quality, very slow

### CFG Scale (Prompt Adherence)
- **3-5**: More creative, less prompt adherence
- **7-9**: Balanced (recommended)
- **10-15**: Strong prompt adherence
- **15+**: May over-saturate

### FPS (Smoothness)
- **4-6**: Slideshow-like
- **8-12**: Smooth motion (recommended)
- **15-24**: Very smooth, cinematic
- **30+**: Ultra-smooth (overkill for most uses)

## 🔧 Troubleshooting

### Generation Too Slow
- Use `--resolution low`
- Reduce `--steps` to 8-12
- Reduce `--duration` and `--fps`
- Lower `--cfg` to 6

### Poor Quality
- Increase `--steps` to 30-50
- Use `--resolution high`
- Increase `--cfg` to 9-12
- Improve your prompt description

### Out of Memory
- Use `--resolution low` or `medium`
- Reduce `--duration`
- Lower `--fps`
- Restart ComfyUI

### No Animation
- Increase `--denoise` to 0.9
- Add motion keywords to prompt
- Use more frames (higher duration/fps)

## 💡 Pro Tips

1. **Start Small**: Always test with low resolution first
2. **Prompt Engineering**: Use descriptive, motion-focused prompts
3. **Batch Generation**: Generate multiple versions with different seeds
4. **Format Planning**: Choose format based on intended platform
5. **Quality vs Time**: Balance quality needs with generation time

## 🎬 Best Practices

- **Mobile Content**: Use vertical format, 576x1024 or higher
- **Web Content**: Use horizontal format, 1024x576 or higher
- **Social Posts**: Use square format, 512x512 or higher
- **Professional**: Use high resolution with 30+ steps
- **Testing**: Always use low resolution and short duration first
