#!/usr/bin/env python3
"""
Simple Image-to-Video (I2V) Script using ComfyUI and Stable Video Diffusion
This script takes an image and generates a video animation from it.
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os
import base64

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI and return the prompt ID"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def get_history(prompt_id, server_address="127.0.0.1:8188"):
    """Get the execution history for a prompt ID"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/history/{prompt_id}") as response:
            return json.loads(response.read())
    except Exception as e:
        print(f"Error getting history: {e}")
        return None

def wait_for_completion(prompt_id, server_address="127.0.0.1:8188", timeout=600):
    """Wait for prompt execution to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id, server_address)
        if history and prompt_id in history:
            return history[prompt_id]
        time.sleep(3)
    return None

def upload_image(image_path, server_address="127.0.0.1:8188"):
    """Upload an image to ComfyUI"""
    try:
        with open(image_path, 'rb') as f:
            files = {'image': f}
            # Note: This is a simplified version. ComfyUI's actual upload API might differ
            print(f"Image loaded: {image_path}")
            return os.path.basename(image_path)
    except Exception as e:
        print(f"Error uploading image: {e}")
        return None

def create_i2v_workflow(image_filename, output_filename="output_video.mp4", vertical=False):
    """Create an image-to-video workflow using SVD"""
    # Set dimensions for vertical (9:16) or horizontal (16:9) video
    if vertical:
        width, height = 576, 1024  # Vertical format
    else:
        width, height = 1024, 576  # Horizontal format

    workflow = {
        "1": {
            "inputs": {
                "image": image_filename,
                "upload": "image"
            },
            "class_type": "LoadImage",
            "_meta": {"title": "Load Image"}
        },
        "2": {
            "inputs": {
                "ckpt_name": "svd_xt.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load SVD Checkpoint"}
        },
        "3": {
            "inputs": {
                "width": width,
                "height": height,
                "video_frames": 25,
                "motion_bucket_id": 127,
                "fps": 8,
                "augmentation_level": 0.0,
                "model": ["2", 0],
                "vae": ["2", 2],
                "clip_vision": ["2", 1],
                "init_image": ["1", 0]
            },
            "class_type": "SVD_img2vid_Conditioning",
            "_meta": {"title": "SVD img2vid Conditioning"}
        },
        "4": {
            "inputs": {
                "seed": 42,
                "steps": 20,
                "cfg": 2.5,
                "sampler_name": "euler",
                "scheduler": "karras",
                "denoise": 1.0,
                "model": ["3", 0],
                "positive": ["3", 1],
                "negative": ["3", 2],
                "latent_image": ["3", 3]
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "5": {
            "inputs": {
                "samples": ["4", 0],
                "vae": ["2", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "VAE Decode"}
        },
        "6": {
            "inputs": {
                "filename_prefix": "vertical_video" if vertical else "i2v_output",
                "fps": 8,
                "lossless": False,
                "quality": 85,
                "method": "h264-mp4",
                "images": ["5", 0]
            },
            "class_type": "VHS_VideoCombine",
            "_meta": {"title": "Video Combine"}
        }
    }
    return workflow

def image_to_video(image_path, output_filename="output_video.mp4", vertical=False):
    """Generate a video from an input image"""
    format_type = "vertical (9:16)" if vertical else "horizontal (16:9)"
    print(f"Generating {format_type} video from image: {image_path}")

    # Check if image exists
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        return False

    # Upload image (simplified - in practice you'd use ComfyUI's upload API)
    image_filename = upload_image(image_path)
    if not image_filename:
        print("Failed to upload image")
        return False

    # Create workflow
    workflow = create_i2v_workflow(image_filename, output_filename, vertical)

    # Queue the prompt
    result = queue_prompt(workflow)
    if not result:
        print("Failed to queue prompt")
        return False

    prompt_id = result['prompt_id']
    print(f"Prompt queued with ID: {prompt_id}")

    # Wait for completion
    print("Waiting for video generation to complete...")
    print("This may take several minutes depending on your hardware...")
    history = wait_for_completion(prompt_id, timeout=600)

    if history:
        print("✅ Video generation completed!")
        print("Check the ComfyUI output folder for your generated video.")
        return True
    else:
        print("❌ Generation timed out or failed")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python simple_i2v.py path/to/image.jpg [--vertical]")
        print("Example: python simple_i2v.py input_image.jpg")
        print("Example: python simple_i2v.py input_image.jpg --vertical")
        sys.exit(1)

    image_path = sys.argv[1]
    vertical = "--vertical" in sys.argv

    success = image_to_video(image_path, vertical=vertical)

    if success:
        format_type = "vertical" if vertical else "horizontal"
        print(f"\n🎉 Success! Generated {format_type} video!")
        print("Check the ComfyUI output folder for your generated video.")
        print("Note: Make sure you have the SVD model downloaded and the VHS extension installed.")
    else:
        print("\n❌ Failed to generate video. Make sure:")
        print("1. ComfyUI is running")
        print("2. SVD model is downloaded")
        print("3. Image file exists and is accessible")
