#!/usr/bin/env python3
"""
Complete Video Generator with All Arguments
This script provides comprehensive control over video generation parameters
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os
import argparse
import glob
from PIL import Image

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI and return the prompt ID"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def get_history(prompt_id, server_address="127.0.0.1:8188"):
    """Get the execution history for a prompt ID"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/history/{prompt_id}") as response:
            return json.loads(response.read())
    except Exception as e:
        print(f"Error getting history: {e}")
        return None

def wait_for_completion(prompt_id, server_address="127.0.0.1:8188", timeout=900):
    """Wait for prompt execution to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id, server_address)
        if history and prompt_id in history:
            return history[prompt_id]
        time.sleep(3)
        print(".", end="", flush=True)
    return None

def create_video_workflow(args):
    """Create a video generation workflow based on arguments"""
    
    # Calculate frames based on duration and fps
    total_frames = int(args.duration * args.fps)
    
    # Set resolution based on format
    if args.format == "vertical":
        if args.resolution == "low":
            width, height = 256, 456
        elif args.resolution == "medium":
            width, height = 576, 1024
        elif args.resolution == "high":
            width, height = 1080, 1920
        else:  # custom
            width, height = args.custom_width, args.custom_height
    elif args.format == "horizontal":
        if args.resolution == "low":
            width, height = 456, 256
        elif args.resolution == "medium":
            width, height = 1024, 576
        elif args.resolution == "high":
            width, height = 1920, 1080
        else:  # custom
            width, height = args.custom_width, args.custom_height
    else:  # square
        if args.resolution == "low":
            width, height = 256, 256
        elif args.resolution == "medium":
            width, height = 512, 512
        elif args.resolution == "high":
            width, height = 1024, 1024
        else:  # custom
            width, height = args.custom_width, args.custom_height

    workflow = {
        "1": {
            "inputs": {
                "width": width,
                "height": height,
                "batch_size": total_frames
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Video Latent"}
        },
        "2": {
            "inputs": {
                "ckpt_name": args.model
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Model"}
        },
        "3": {
            "inputs": {
                "text": args.prompt,
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Positive Prompt"}
        },
        "4": {
            "inputs": {
                "text": args.negative_prompt,
                "clip": ["2", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "Negative Prompt"}
        },
        "5": {
            "inputs": {
                "seed": args.seed,
                "steps": args.steps,
                "cfg": args.cfg,
                "sampler_name": args.sampler,
                "scheduler": args.scheduler,
                "denoise": args.denoise,
                "model": ["2", 0],
                "positive": ["3", 0],
                "negative": ["4", 0],
                "latent_image": ["1", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "Video Generation"}
        },
        "6": {
            "inputs": {
                "samples": ["5", 0],
                "vae": ["2", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "Decode Video"}
        },
        "7": {
            "inputs": {
                "filename_prefix": args.output_prefix,
                "images": ["6", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Frames"}
        }
    }
    return workflow

def create_gif_from_frames(output_prefix, fps, duration):
    """Create an animated GIF from generated frames"""
    print("\n🎬 Creating animated GIF from frames...")
    
    # Find all frame files
    frame_pattern = f"output/{output_prefix}_*.png"
    frame_files = sorted(glob.glob(frame_pattern))
    
    if not frame_files:
        print("❌ No frame files found!")
        return False
    
    print(f"✅ Found {len(frame_files)} frames")
    
    # Load frames
    frames = []
    for frame_file in frame_files:
        try:
            img = Image.open(frame_file)
            frames.append(img)
        except Exception as e:
            print(f"❌ Error loading {frame_file}: {e}")
            return False
    
    if not frames:
        return False
    
    # Create GIF
    output_file = f"{output_prefix}_video.gif"
    frame_duration = int(1000 / fps)  # milliseconds per frame
    
    try:
        frames[0].save(
            output_file,
            save_all=True,
            append_images=frames[1:],
            duration=frame_duration,
            loop=0
        )
        
        file_size = os.path.getsize(output_file) / 1024  # KB
        print(f"✅ GIF created: {output_file} ({file_size:.1f} KB)")
        return True
        
    except Exception as e:
        print(f"❌ Error creating GIF: {e}")
        return False

def generate_video(args):
    """Main video generation function"""
    
    # Print generation settings
    print("🎬 Video Generation Settings:")
    print("=" * 50)
    print(f"📐 Format: {args.format}")
    print(f"📏 Resolution: {args.resolution}")
    print(f"⏱️  Duration: {args.duration} seconds")
    print(f"🎞️  FPS: {args.fps}")
    print(f"🖼️  Total Frames: {int(args.duration * args.fps)}")
    print(f"🎨 Prompt: {args.prompt}")
    print(f"🚫 Negative: {args.negative_prompt}")
    print(f"🎯 Steps: {args.steps}")
    print(f"⚙️  CFG: {args.cfg}")
    print(f"🎲 Seed: {args.seed}")
    print(f"🔧 Model: {args.model}")
    print("=" * 50)
    
    # Check if ComfyUI is running
    try:
        with urllib.request.urlopen("http://127.0.0.1:8188/system_stats", timeout=5) as response:
            print("✅ ComfyUI is running")
    except:
        print("❌ ComfyUI is not running. Please start it with: python main.py")
        return False
    
    # Create workflow
    workflow = create_video_workflow(args)
    
    # Queue the prompt
    print("📤 Sending generation request to ComfyUI...")
    result = queue_prompt(workflow)
    if not result:
        print("❌ Failed to queue prompt")
        return False
    
    prompt_id = result['prompt_id']
    print(f"✅ Generation queued with ID: {prompt_id}")
    
    # Wait for completion
    print("⏳ Generating video frames...")
    print("💡 This may take several minutes depending on your settings...")
    
    # Calculate estimated time
    total_frames = int(args.duration * args.fps)
    estimated_time = (total_frames * args.steps * 2) / 60  # rough estimate in minutes
    print(f"⏰ Estimated time: ~{estimated_time:.1f} minutes")
    
    history = wait_for_completion(prompt_id, timeout=args.timeout)
    
    if history:
        print("\n✅ Frame generation completed!")
        
        # Create GIF if requested
        if args.create_gif:
            gif_success = create_gif_from_frames(args.output_prefix, args.fps, args.duration)
            if gif_success:
                print("🎉 GIF creation successful!")
        
        print(f"\n📁 Files location: ComfyUI/output/")
        print(f"🖼️  Frames: {args.output_prefix}_*.png")
        if args.create_gif:
            print(f"🎥 GIF: {args.output_prefix}_video.gif")
        
        return True
    else:
        print("\n❌ Generation timed out or failed")
        return False

def main():
    """Main function with argument parsing"""
    parser = argparse.ArgumentParser(
        description="Complete Video Generator with All Arguments",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Quick test (low quality, fast)
  python video_generator.py --prompt "sunset" --format vertical --resolution low --duration 1 --fps 4

  # Medium quality vertical video
  python video_generator.py --prompt "beautiful landscape" --format vertical --resolution medium --duration 3 --fps 8

  # High quality horizontal video
  python video_generator.py --prompt "ocean waves" --format horizontal --resolution high --duration 5 --fps 12 --steps 30

  # Custom resolution
  python video_generator.py --prompt "city skyline" --format custom --custom-width 800 --custom-height 600 --duration 2

  # Professional settings
  python video_generator.py --prompt "cinematic forest" --format vertical --resolution high --duration 4 --fps 24 --steps 50 --cfg 12
        """
    )
    
    # Required arguments
    parser.add_argument("--prompt", required=True, help="Text prompt for video generation")
    
    # Format and resolution
    parser.add_argument("--format", choices=["vertical", "horizontal", "square", "custom"], 
                       default="vertical", help="Video format (default: vertical)")
    parser.add_argument("--resolution", choices=["low", "medium", "high", "custom"], 
                       default="medium", help="Video resolution (default: medium)")
    parser.add_argument("--custom-width", type=int, default=512, help="Custom width (for custom resolution)")
    parser.add_argument("--custom-height", type=int, default=512, help="Custom height (for custom resolution)")
    
    # Timing
    parser.add_argument("--duration", type=float, default=2.0, help="Video duration in seconds (default: 2.0)")
    parser.add_argument("--fps", type=int, default=8, help="Frames per second (default: 8)")
    
    # Quality settings
    parser.add_argument("--steps", type=int, default=20, help="Sampling steps (default: 20)")
    parser.add_argument("--cfg", type=float, default=7.0, help="CFG scale (default: 7.0)")
    parser.add_argument("--denoise", type=float, default=0.8, help="Denoise strength (default: 0.8)")
    
    # Advanced settings
    parser.add_argument("--negative-prompt", default="blurry, low quality, distorted", 
                       help="Negative prompt (default: 'blurry, low quality, distorted')")
    parser.add_argument("--seed", type=int, default=42, help="Random seed (default: 42)")
    parser.add_argument("--sampler", default="euler", help="Sampler name (default: euler)")
    parser.add_argument("--scheduler", default="normal", help="Scheduler (default: normal)")
    parser.add_argument("--model", default="v1-5-pruned-emaonly.safetensors", 
                       help="Model checkpoint (default: v1-5-pruned-emaonly.safetensors)")
    
    # Output settings
    parser.add_argument("--output-prefix", default="generated_video", help="Output filename prefix")
    parser.add_argument("--create-gif", action="store_true", help="Create animated GIF from frames")
    parser.add_argument("--timeout", type=int, default=1800, help="Generation timeout in seconds (default: 1800)")
    
    args = parser.parse_args()
    
    # Validate arguments
    if args.format == "custom" and args.resolution != "custom":
        print("⚠️  Warning: Using custom format, setting resolution to custom")
        args.resolution = "custom"
    
    if args.duration <= 0:
        print("❌ Duration must be positive")
        return False
    
    if args.fps <= 0:
        print("❌ FPS must be positive")
        return False
    
    # Generate video
    success = generate_video(args)
    
    if success:
        print("\n🎉 Video generation completed successfully!")
    else:
        print("\n❌ Video generation failed")
    
    return success

if __name__ == "__main__":
    main()
