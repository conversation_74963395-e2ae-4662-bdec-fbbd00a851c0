# ComfyUI Stable Diffusion Video Setup

This setup provides an easy way to run Stable Diffusion video generation on your Mac M1 Pro.

## 🚀 Quick Start

### 1. Start ComfyUI
```bash
cd ComfyUI
python main.py
```
Then open http://127.0.0.1:8188 in your browser.

### 2. Test Installation
```bash
python test_installation.py
```

### 3. Generate Videos

#### Text-to-Video (T2V)
```bash
python simple_t2v.py "a beautiful sunset over mountains"
```

#### Image-to-Video (I2V)
```bash
python simple_i2v.py path/to/your/image.jpg
```

#### Video-to-Video (V2V)
```bash
python simple_v2v.py path/to/your/video.mp4 "cartoon style" 0.7
```

## 📁 Directory Structure

```
ComfyUI/
├── models/
│   ├── checkpoints/
│   │   ├── v1-5-pruned-emaonly.safetensors  # Stable Diffusion 1.5
│   │   └── svd_xt.safetensors               # Stable Video Diffusion
│   ├── vae/
│   └── clip_vision/
├── output/                                   # Generated content appears here
├── simple_t2v.py                           # Text-to-video script
├── simple_i2v.py                           # Image-to-video script
├── simple_v2v.py                           # Video-to-video script
└── test_installation.py                    # Test script
```

## 🎯 What Each Script Does

### simple_t2v.py
- Generates an image from text prompt
- Note: Full T2V requires additional setup with SVD

### simple_i2v.py
- Takes an input image and creates a video animation
- Uses Stable Video Diffusion (SVD) model
- Requires SVD model to be downloaded

### simple_v2v.py
- Transforms an existing video with AI
- Can apply style changes or effects
- Requires video processing extensions

### test_installation.py
- Checks if ComfyUI is running
- Verifies models are downloaded
- Tests basic generation functionality

## 🔧 System Requirements

- **Hardware**: Mac with Apple Silicon (M1/M2/M3/M4)
- **RAM**: 16GB+ recommended
- **Storage**: ~15GB for models
- **Python**: 3.11+ with PyTorch

## 📦 Models

### Required Models:
1. **v1-5-pruned-emaonly.safetensors** (~4GB)
   - Basic Stable Diffusion model for image generation

2. **svd_xt.safetensors** (~9GB)
   - Stable Video Diffusion model for video generation

### Model Download Status:
Run `python test_installation.py` to check download progress.

## 🎨 Usage Examples

### Basic Image Generation
```bash
python simple_t2v.py "a serene lake at sunset"
```

### Animate an Image
```bash
python simple_i2v.py my_photo.jpg
```

### Transform Video Style
```bash
python simple_v2v.py input_video.mp4 "oil painting style" 0.8
```

## 🛠 Troubleshooting

### ComfyUI Won't Start
- Check Python version: `python --version`
- Reinstall dependencies: `pip install -r requirements.txt`

### Models Not Found
- Check download progress in terminal
- Ensure sufficient disk space (~15GB)

### Generation Fails
- Verify ComfyUI is running at http://127.0.0.1:8188
- Check model files are completely downloaded
- Monitor system memory usage

### Performance Tips
- Close other applications to free RAM
- Use lower resolution for faster generation
- Reduce number of steps for quicker results

## 🔗 Useful Links

- [ComfyUI GitHub](https://github.com/comfyanonymous/ComfyUI)
- [Stable Video Diffusion](https://huggingface.co/stabilityai/stable-video-diffusion-img2vid-xt)
- [ComfyUI Documentation](https://docs.comfy.org/)

## 📝 Notes

- First generation may take longer as models load
- Generated content appears in the `output/` folder
- Video generation requires significant processing time
- M1 Pro with 16GB RAM is well-suited for this setup

## 🎉 Next Steps

1. Explore the ComfyUI web interface
2. Try different prompts and settings
3. Install additional custom nodes for more features
4. Experiment with different models and styles

Happy creating! 🎬✨
