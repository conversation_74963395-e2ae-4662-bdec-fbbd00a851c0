{"id": "bc7448ba-118f-4226-95ab-32227992f954", "revision": 0, "last_node_id": 6, "last_link_id": 4, "nodes": [{"id": 1, "type": "VHS_LoadVideo", "pos": [54, 89], "size": [245.1999969482422, 492.7751770019531], "flags": {}, "order": 0, "mode": 0, "inputs": [{"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "slot_index": 0, "links": [1, 2, 3, 4]}, {"name": "frame_count", "type": "INT", "links": null}, {"name": "audio", "type": "AUDIO", "links": null}, {"name": "video_info", "type": "VHS_VIDEOINFO", "links": null}], "properties": {"Node name for S&R": "VHS_LoadVideo"}, "widgets_values": {"video": "leader.webm", "force_rate": 8, "custom_width": 512, "custom_height": 0, "frame_load_cap": 64, "skip_first_frames": 1, "select_every_nth": 1, "format": "AnimateDiff", "choose video to upload": "image", "videopreview": {"hidden": false, "paused": false, "params": {"frame_load_cap": 64, "skip_first_frames": 0, "force_rate": 8, "filename": "leader.webm", "type": "input", "format": "video/mp4", "select_every_nth": 1}}}}, {"id": 4, "type": "VHS_VideoCombine", "pos": [630.9500122070312, 136.90997314453125], "size": [315, 497.25], "flags": {}, "order": 2, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 2}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/ProRes", "profile": "2", "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00004.mov", "subfolder": "", "type": "temp", "format": "video/ProRes", "frame_rate": 8, "workflow": "AnimateDiff_00004.png"}}}}, {"id": 3, "type": "VHS_VideoCombine", "pos": [635.0499267578125, -407.1000061035156], "size": [315, 497.25], "flags": {}, "order": 1, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 1}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/ProRes", "profile": "1", "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00001.mov", "subfolder": "", "type": "temp", "format": "video/ProRes", "frame_rate": 8, "workflow": "AnimateDiff_00001.png"}}}}, {"id": 5, "type": "VHS_VideoCombine", "pos": [974.6401977539062, -409.33984375], "size": [315, 497.25], "flags": {}, "order": 3, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 3}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/ProRes", "profile": "3", "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00002.mov", "subfolder": "", "type": "temp", "format": "video/ProRes", "frame_rate": 8, "workflow": "AnimateDiff_00002.png"}}}}, {"id": 6, "type": "VHS_VideoCombine", "pos": [968.7000122070312, 138.7698974609375], "size": [315, 497.25], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 4}, {"name": "audio", "shape": 7, "type": "AUDIO", "link": null}, {"name": "meta_batch", "shape": 7, "type": "VHS_BatchManager", "link": null}, {"name": "vae", "shape": 7, "type": "VAE", "link": null}], "outputs": [{"name": "Filenames", "type": "VHS_FILENAMES", "links": null}], "properties": {"Node name for S&R": "VHS_VideoCombine"}, "widgets_values": {"frame_rate": 8, "loop_count": 0, "filename_prefix": "AnimateDiff", "format": "video/ProRes", "profile": "4", "pingpong": false, "save_output": false, "videopreview": {"hidden": false, "paused": false, "params": {"filename": "AnimateDiff_00003.mov", "subfolder": "", "type": "temp", "format": "video/ProRes", "frame_rate": 8}}}}], "links": [[1, 1, 0, 3, 0, "IMAGE"], [2, 1, 0, 4, 0, "IMAGE"], [3, 1, 0, 5, 0, "IMAGE"], [4, 1, 0, 6, 0, "IMAGE"]], "groups": [], "config": {}, "extra": {"frontendVersion": "1.17.0", "VHS_latentpreview": true, "VHS_latentpreviewrate": 0, "VHS_MetadataImage": true, "VHS_KeepIntermediate": true}, "version": 0.4, "tests": {"6": [{"type": "video", "key": "pix_fmt", "value": "yuv444p12le"}], "length": 1}}