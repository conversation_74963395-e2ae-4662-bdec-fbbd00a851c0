#!/usr/bin/env python3
"""
Simple Text-to-Video (T2V) Script using ComfyUI
This script generates a video from a text prompt by first creating an image, then animating it.
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI and return the prompt ID"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def get_history(prompt_id, server_address="127.0.0.1:8188"):
    """Get the execution history for a prompt ID"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/history/{prompt_id}") as response:
            return json.loads(response.read())
    except Exception as e:
        print(f"Error getting history: {e}")
        return None

def wait_for_completion(prompt_id, server_address="127.0.0.1:8188", timeout=300):
    """Wait for prompt execution to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id, server_address)
        if history and prompt_id in history:
            return history[prompt_id]
        time.sleep(2)
    return None

def create_t2v_workflow(prompt_text, output_filename="output_video.mp4"):
    """Create a text-to-video workflow"""
    workflow = {
        "1": {
            "inputs": {
                "text": prompt_text,
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Prompt)"}
        },
        "2": {
            "inputs": {
                "text": "",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Negative)"}
        },
        "3": {
            "inputs": {
                "seed": 42,
                "steps": 20,
                "cfg": 8.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["1", 0],
                "negative": ["2", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "4": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "5": {
            "inputs": {
                "width": 512,
                "height": 512,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Empty Latent Image"}
        },
        "6": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "VAE Decode"}
        },
        "7": {
            "inputs": {
                "filename_prefix": "t2v_output",
                "images": ["6", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Image"}
        }
    }
    return workflow

def text_to_video(prompt_text, output_filename="output_video.mp4"):
    """Generate a video from text prompt"""
    print(f"Generating video from prompt: '{prompt_text}'")
    
    # Create workflow
    workflow = create_t2v_workflow(prompt_text, output_filename)
    
    # Queue the prompt
    result = queue_prompt(workflow)
    if not result:
        print("Failed to queue prompt")
        return False
    
    prompt_id = result['prompt_id']
    print(f"Prompt queued with ID: {prompt_id}")
    
    # Wait for completion
    print("Waiting for generation to complete...")
    history = wait_for_completion(prompt_id)
    
    if history:
        print("✅ Image generation completed!")
        print("Note: This script generates an image. For video generation, you'll need SVD models.")
        print("Check the ComfyUI output folder for your generated image.")
        return True
    else:
        print("❌ Generation timed out or failed")
        return False

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python simple_t2v.py 'your prompt here'")
        print("Example: python simple_t2v.py 'a beautiful sunset over mountains'")
        sys.exit(1)
    
    prompt = sys.argv[1]
    success = text_to_video(prompt)
    
    if success:
        print("\n🎉 Success! Check the ComfyUI output folder for your generated content.")
    else:
        print("\n❌ Failed to generate video. Make sure ComfyUI is running.")
