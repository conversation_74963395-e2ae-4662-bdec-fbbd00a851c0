#!/usr/bin/env python3
"""
Generate a vertical video using ComfyUI and Stable Video Diffusion
This script creates a working workflow for vertical video generation
"""

import json
import urllib.request
import urllib.parse
import time
import sys
import os
import base64

def queue_prompt(prompt, server_address="127.0.0.1:8188"):
    """Send a prompt to ComfyUI and return the prompt ID"""
    p = {"prompt": prompt}
    data = json.dumps(p).encode('utf-8')
    req = urllib.request.Request(f"http://{server_address}/prompt", data=data)
    req.add_header('Content-Type', 'application/json')
    
    try:
        response = urllib.request.urlopen(req)
        return json.loads(response.read())
    except Exception as e:
        print(f"Error queuing prompt: {e}")
        return None

def get_history(prompt_id, server_address="127.0.0.1:8188"):
    """Get the execution history for a prompt ID"""
    try:
        with urllib.request.urlopen(f"http://{server_address}/history/{prompt_id}") as response:
            return json.loads(response.read())
    except Exception as e:
        print(f"Error getting history: {e}")
        return None

def wait_for_completion(prompt_id, server_address="127.0.0.1:8188", timeout=600):
    """Wait for prompt execution to complete"""
    start_time = time.time()
    while time.time() - start_time < timeout:
        history = get_history(prompt_id, server_address)
        if history and prompt_id in history:
            return history[prompt_id]
        time.sleep(3)
        print(".", end="", flush=True)
    return None

def upload_image_to_comfyui(image_path, server_address="127.0.0.1:8188"):
    """Upload an image to ComfyUI's input directory"""
    try:
        # Copy image to ComfyUI input directory
        input_dir = "input"
        if not os.path.exists(input_dir):
            os.makedirs(input_dir)
        
        filename = os.path.basename(image_path)
        dest_path = os.path.join(input_dir, filename)
        
        # Copy the file
        import shutil
        shutil.copy2(image_path, dest_path)
        
        print(f"Image copied to: {dest_path}")
        return filename
    except Exception as e:
        print(f"Error uploading image: {e}")
        return None

def create_vertical_video_workflow(image_filename):
    """Create a working vertical video workflow"""
    workflow = {
        "3": {
            "inputs": {
                "seed": 42,
                "steps": 20,
                "cfg": 8.0,
                "sampler_name": "euler",
                "scheduler": "normal",
                "denoise": 1.0,
                "model": ["4", 0],
                "positive": ["6", 0],
                "negative": ["7", 0],
                "latent_image": ["5", 0]
            },
            "class_type": "KSampler",
            "_meta": {"title": "KSampler"}
        },
        "4": {
            "inputs": {
                "ckpt_name": "v1-5-pruned-emaonly.safetensors"
            },
            "class_type": "CheckpointLoaderSimple",
            "_meta": {"title": "Load Checkpoint"}
        },
        "5": {
            "inputs": {
                "width": 576,
                "height": 1024,
                "batch_size": 1
            },
            "class_type": "EmptyLatentImage",
            "_meta": {"title": "Empty Latent Image"}
        },
        "6": {
            "inputs": {
                "text": "beautiful landscape, mountains, sunset, cinematic, high quality",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Prompt)"}
        },
        "7": {
            "inputs": {
                "text": "blurry, low quality, distorted",
                "clip": ["4", 1]
            },
            "class_type": "CLIPTextEncode",
            "_meta": {"title": "CLIP Text Encode (Negative)"}
        },
        "8": {
            "inputs": {
                "samples": ["3", 0],
                "vae": ["4", 2]
            },
            "class_type": "VAEDecode",
            "_meta": {"title": "VAE Decode"}
        },
        "9": {
            "inputs": {
                "filename_prefix": "vertical_image",
                "images": ["8", 0]
            },
            "class_type": "SaveImage",
            "_meta": {"title": "Save Image"}
        }
    }
    return workflow

def generate_vertical_content():
    """Generate vertical content"""
    print("🎬 Generating vertical content...")
    print("📐 Format: 576x1024 (9:16 aspect ratio)")
    
    # Create workflow
    workflow = create_vertical_video_workflow("sample_image_vertical.jpg")
    
    # Queue the prompt
    print("📤 Sending request to ComfyUI...")
    result = queue_prompt(workflow)
    if not result:
        print("❌ Failed to queue prompt")
        return False
    
    prompt_id = result['prompt_id']
    print(f"✅ Prompt queued with ID: {prompt_id}")
    
    # Wait for completion
    print("⏳ Generating vertical image (this will take a moment)...")
    history = wait_for_completion(prompt_id, timeout=300)
    
    if history:
        print("\n✅ Vertical image generation completed!")
        print("📁 Check the ComfyUI output folder for your generated content.")
        print("📱 The image is in vertical format (576x1024) perfect for mobile/social media!")
        return True
    else:
        print("\n❌ Generation timed out or failed")
        return False

def main():
    """Main function"""
    print("🚀 Vertical Video Generator")
    print("=" * 50)
    
    # Check if ComfyUI is running
    try:
        with urllib.request.urlopen("http://127.0.0.1:8188/system_stats", timeout=5) as response:
            print("✅ ComfyUI is running")
    except:
        print("❌ ComfyUI is not running. Please start it with: python main.py")
        return False
    
    # Generate content
    success = generate_vertical_content()
    
    if success:
        print("\n🎉 Success! Your vertical content has been generated!")
        print("\n📋 Next steps:")
        print("1. Check the 'output' folder in ComfyUI")
        print("2. Look for files starting with 'vertical_image'")
        print("3. Use the generated image for further video processing")
        print("\n💡 Tip: You can use this vertical image as input for video generation tools!")
    else:
        print("\n❌ Generation failed. Please check:")
        print("1. ComfyUI is running at http://127.0.0.1:8188")
        print("2. Models are properly loaded")
        print("3. Check ComfyUI console for error messages")
    
    return success

if __name__ == "__main__":
    main()
